import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:rolio/common/constants/ws_constants.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/message_tracker.dart';
import 'package:rolio/manager/ws_connection_manager.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/state/global_event_state.dart';


/// WebSocket消息结构
class WsMsg {
  final WsEvent event; // 事件类型
  final dynamic data; // 数据
  final int? error; // 错误码
  final int? conversationid; // 会话ID

  WsMsg({
    required this.event,
    required this.data,
    this.error,
    this.conversationid,
  });

  factory WsMsg.fromJson(Map<String, dynamic> json) {
    return WsMsg(
      event: WsEvent.values.firstWhere(
        (e) => e.toString() == 'WsEvent.${json['event']}',
        orElse: () => WsEvent.unknown,
      ),
      data: json['data'],
      error: json['error'] as int?,
      conversationid: json['conversation_id'] as int?,
    );
  }

  Map<String, dynamic> toJson() => {
    'event': event.toString().split('.').last,
    'data': data is Map<String, dynamic> 
        ? data
        : {'content': data},
    if (error != null) 'error': error,
    if (conversationid != null) 'conversation_id': conversationid,
  };
  
  // 提取conversationid的便捷方法
  int? get channelId {
    // 优先使用本身的conversationid属性
    if (conversationid != null) {
      return conversationid;
    }
    
    // 其次从data中提取
    if (data is Map) {
      final mapData = data as Map;
      return mapData['conversation_id'] as int?;
    }
    return null;
  }
}

/// WebSocket消息管理器
///
/// 负责WebSocket消息的管理，包括：
/// - 消息序列化/反序列化
/// - 事件分发和订阅
/// - 通道管理
/// - 消息路由
class WsMessageManager extends GetxController {
  // 单例模式
  static final WsMessageManager _instance = WsMessageManager._internal();
  factory WsMessageManager() => _instance;

  // 连接管理器
  late final WsConnectionManager _connectionManager;

  // 通道管理
  final RxnInt _currentConversationId = RxnInt();

  // GetX响应式事件管理 - 替代BehaviorSubject
  final RxMap<int, RxList<WsMsg>> _channelEventStreams = <int, RxList<WsMsg>>{}.obs;
  final RxMap<WsEvent, RxList<WsMsg>> _eventStreams = <WsEvent, RxList<WsMsg>>{}.obs;

  // 订阅者计数管理
  final RxMap<int, RxInt> _channelSubscriberCount = <int, RxInt>{}.obs;
  final RxMap<WsEvent, RxInt> _eventSubscriberCount = <WsEvent, RxInt>{}.obs;

  // 消息跟踪器 - 仅跟踪待处理消息
  final MessageTracker _messageTracker = MessageTracker();

  // 订阅
  StreamSubscription? _messageSubscription;

  // Stream.periodic订阅 - 替代Timer
  StreamSubscription? _cleanupSubscription;
  
  // 构造函数
  WsMessageManager._internal();

  @override
  void onInit() {
    super.onInit();
    try {
      // 确保WsConnectionManager已初始化 - 如果没有，先创建一个
      if (!Get.isRegistered<WsConnectionManager>()) {
        LogUtil.warn('WsConnectionManager未注册，尝试先初始化它');
        // 创建并自动注册WsConnectionManager
        WsConnectionManager();
      }

      // 获取WebSocket连接管理器实例 - 现在应该总是能找到它
      _connectionManager = Get.find<WsConnectionManager>();
      LogUtil.debug('WsMessageManager成功获取WsConnectionManager实例');

      _setupMessageListener();

      // 设置GetX Worker替代Timer清理任务
      _setupCleanupWorker();

      LogUtil.debug('WsMessageManager初始化完成');
    } catch (e) {
      LogUtil.error('WsMessageManager初始化失败: $e');
      throw Exception('WsMessageManager初始化失败: $e');
    }
  }

  @override
  void onClose() {
    // 停止监听消息
    _messageSubscription?.cancel();

    // 停止清理任务
    _cleanupSubscription?.cancel();

    // 清理所有响应式变量
    _eventStreams.clear();
    _channelEventStreams.clear();
    _channelSubscriberCount.clear();
    _eventSubscriberCount.clear();

    super.onClose();
    LogUtil.debug('WsMessageManager资源清理完成');
  }
  
  /// 设置Stream.periodic清理任务 - 使用Stream.periodic替代Timer.periodic
  void _setupCleanupWorker() {
    // 使用Stream.periodic创建周期性清理任务
    final cleanupStream = Stream.periodic(
      const Duration(seconds: 15),
      (count) => count,
    );

    // 监听清理流
    _cleanupSubscription = cleanupStream.listen((_) {
      _performCleanup();
    });

    LogUtil.debug('已设置Stream.periodic定期清理任务，间隔15秒');
  }

  /// 执行清理操作 - 替代原有的清理方法
  void _performCleanup() {
    try {
      _cleanupExpiredStreams();
      _cleanupEmptyStreams();
      LogUtil.debug('GetX响应式清理完成');
    } catch (e) {
      LogUtil.error('GetX响应式清理失败: $e');
    }
  }
  
  /// 清理过期的响应式流 - 替代原有的_cleanupHandlers
  void _cleanupExpiredStreams() {
    try {
      int totalRemovedStreams = 0;

      // 清理事件流中的过期消息（保留最近50条）
      final eventKeysToRemove = <WsEvent>[];
      _eventStreams.forEach((event, messages) {
        if (messages.length > 50) {
          final excessCount = messages.length - 50;
          messages.removeRange(0, excessCount);
          totalRemovedStreams += excessCount;
        }

        // 如果没有订阅者且消息为空，标记为移除
        if (messages.isEmpty && (_eventSubscriberCount[event]?.value ?? 0) == 0) {
          eventKeysToRemove.add(event);
        }
      });

      // 移除空的事件流
      for (final key in eventKeysToRemove) {
        _eventStreams.remove(key);
        _eventSubscriberCount.remove(key);
      }

      // 清理通道流中的过期消息
      final channelKeysToRemove = <int>[];
      _channelEventStreams.forEach((channelId, messages) {
        if (messages.length > 50) {
          final excessCount = messages.length - 50;
          messages.removeRange(0, excessCount);
          totalRemovedStreams += excessCount;
        }

        // 如果没有订阅者且消息为空，标记为移除
        if (messages.isEmpty && (_channelSubscriberCount[channelId]?.value ?? 0) == 0) {
          channelKeysToRemove.add(channelId);
        }
      });

      // 移除空的通道流
      for (final key in channelKeysToRemove) {
        _channelEventStreams.remove(key);
        _channelSubscriberCount.remove(key);
      }

      if (totalRemovedStreams > 0) {
        LogUtil.debug('已清理${totalRemovedStreams}条过期消息，移除了${eventKeysToRemove.length + channelKeysToRemove.length}个空流');
      }
    } catch (e) {
      LogUtil.error('清理过期流失败: $e');
    }
  }
  
  /// 清理空的响应式流 - 替代原有的重复订阅清理
  void _cleanupEmptyStreams() {
    try {
      int totalRemovedStreams = 0;

      // 清理没有订阅者的事件流
      final eventKeysToRemove = <WsEvent>[];
      _eventStreams.forEach((event, messages) {
        final subscriberCount = _eventSubscriberCount[event]?.value ?? 0;
        if (subscriberCount == 0 && messages.isEmpty) {
          eventKeysToRemove.add(event);
        }
      });

      for (final key in eventKeysToRemove) {
        _eventStreams.remove(key);
        _eventSubscriberCount.remove(key);
        totalRemovedStreams++;
      }

      // 清理没有订阅者的通道流
      final channelKeysToRemove = <int>[];
      _channelEventStreams.forEach((channelId, messages) {
        final subscriberCount = _channelSubscriberCount[channelId]?.value ?? 0;
        if (subscriberCount == 0 && messages.isEmpty) {
          channelKeysToRemove.add(channelId);
        }
      });

      for (final key in channelKeysToRemove) {
        _channelEventStreams.remove(key);
        _channelSubscriberCount.remove(key);
        totalRemovedStreams++;
      }

      if (totalRemovedStreams > 0) {
        LogUtil.debug('清理完成: 共移除$totalRemovedStreams个空的响应式流');
      }
    } catch (e) {
      LogUtil.error('清理空流失败: $e');
    }
  }
  
  /// 设置消息监听器
  void _setupMessageListener() {
    // 取消现有订阅
    _messageSubscription?.cancel();
    
    // 订阅连接管理器的消息流
    _messageSubscription = _connectionManager.messageStream.listen((rawMessage) {
      if (rawMessage != null) {
        _processRawMessage(rawMessage);
      }
    });
  }
  
  /// 处理原始消息
  void _processRawMessage(String rawMessage) {
    // 添加日志：记录所有接收到的原始消息，不管格式是否正确
    LogUtil.info('WebSocket接收到消息: $rawMessage');
    
    try {
      // 解析JSON
      final Map<String, dynamic> jsonData = json.decode(rawMessage);
      
      // 创建WsMsg对象
      final message = WsMsg.fromJson(jsonData);
      
      // 立即分发消息，不缓存
      _dispatchMessage(message);
    } catch (e) {
      LogUtil.error('解析WebSocket消息失败: $e');
      LogUtil.debug('原始消息: $rawMessage');
    }
  }
  
  /// 分发消息到对应的响应式流
  void _dispatchMessage(WsMsg message) {
    // 首先尝试获取消息中的role_id
    int? roleId;
    if (message.data is Map) {
      final mapData = message.data as Map;
      roleId = mapData['role_id'] is int ? mapData['role_id'] as int :
              (mapData['role_id'] is String ? int.tryParse(mapData['role_id'].toString()) : null);
    }

    // 尝试获取消息中的message_id或reply_to_message_id
    String? messageId;
    if (message.data is Map) {
      final mapData = message.data as Map;
      messageId = mapData['message_id']?.toString() ?? mapData['reply_to_message_id']?.toString();
    }

    // 记录详细的分发信息
    if (message.event != WsEvent.ping && message.event != WsEvent.pong) {
      LogUtil.debug('分发消息: event=${message.event}, role_id=$roleId, conversation_id=${message.channelId}, message_id=$messageId');
      // 添加原始消息内容的日志
      if (message.data is Map) {
        LogUtil.debug('消息内容: ${json.encode(message.data)}');
      }
    }

    // 记录各个响应式流的状态（简化日志，移除用户不需要的conversation_id消息数量信息）
    if (roleId != null) {
      LogUtil.debug('role_id=$roleId 的消息数量: ${_channelEventStreams[roleId]?.length ?? 0}');
    }

    // 优先基于role_id分发消息到响应式流
    if (roleId != null) {
      // 验证角色是否还有效（通过检查MessageTracker中的订阅）
      bool isRoleActive = false;
      try {
        final messageTracker = MessageTracker();
        final subscriptionCount = messageTracker.getRoleSubscriptionCount(roleId);
        isRoleActive = subscriptionCount > 0;

        if (!isRoleActive) {
          LogUtil.warn('角色$roleId在MessageTracker中已无订阅(订阅数=$subscriptionCount)，但仍收到消息，将清理通道流');
          // 清理该角色的响应式流
          _clearChannelStream(roleId);
          return; // 不再继续处理消息
        } else {
          LogUtil.debug('角色$roleId有效，订阅数=$subscriptionCount，继续处理消息');
        }
      } catch (e) {
        LogUtil.error('验证角色$roleId有效性失败: $e');
      }

      // 确保响应式流存在
      if (!_channelEventStreams.containsKey(roleId)) {
        _channelEventStreams[roleId] = <WsMsg>[].obs;
      }

      // 添加消息到响应式流
      _channelEventStreams[roleId]!.add(message);
      LogUtil.debug('消息已分发到role_id=$roleId的响应式流');
    }

    // 其次基于conversation_id分发消息（如果有）
    if (message.channelId != null) {
      // 确保响应式流存在
      if (!_channelEventStreams.containsKey(message.channelId)) {
        _channelEventStreams[message.channelId!] = <WsMsg>[].obs;
      }

      // 添加消息到响应式流
      _channelEventStreams[message.channelId]!.add(message);
      LogUtil.debug('消息已分发到conversation_id=${message.channelId}的响应式流');
    }

    // 同时处理全局事件订阅（不管有没有conversationid都要处理）
    if (message.event != WsEvent.ping && message.event != WsEvent.pong) {
      // 确保全局事件流存在
      if (!_eventStreams.containsKey(message.event)) {
        _eventStreams[message.event] = <WsMsg>[].obs;
      }

      // 添加消息到全局事件流
      _eventStreams[message.event]!.add(message);
      LogUtil.debug('消息已分发到event=${message.event}的全局响应式流');
    }

    // 记录消息分发完成（响应式流会自动通知订阅者）
    LogUtil.debug('消息分发完成: event=${message.event}, role_id=$roleId, conversation_id=${message.channelId}');
  }

  /// 清理指定通道的响应式流
  void _clearChannelStream(int channelId) {
    try {
      _channelEventStreams.remove(channelId);
      _channelSubscriberCount.remove(channelId);
      LogUtil.debug('已清理通道ID=$channelId的响应式流');
    } catch (e) {
      LogUtil.error('清理通道ID=$channelId的响应式流失败: $e');
    }
  }
  
  /// 订阅事件 - 使用GetX响应式流
  Stream<WsMsg> on(WsEvent event) {
    // 确保事件流存在
    if (!_eventStreams.containsKey(event)) {
      _eventStreams[event] = <WsMsg>[].obs;
    }

    // 增加订阅者计数
    if (!_eventSubscriberCount.containsKey(event)) {
      _eventSubscriberCount[event] = 0.obs;
    }
    _eventSubscriberCount[event]!.value++;

    LogUtil.debug('事件${event}订阅者增加，当前订阅数: ${_eventSubscriberCount[event]!.value}');

    // 返回响应式流的stream，展开List<WsMsg>为单个WsMsg
    return _eventStreams[event]!.stream.expand((messageList) => messageList);
  }

  /// 取消订阅 - 使用GetX响应式流
  void off(WsEvent event, {StreamSubscription? subscription}) {
    // 减少订阅者计数
    if (_eventSubscriberCount.containsKey(event)) {
      _eventSubscriberCount[event]!.value--;
      LogUtil.debug('事件${event}订阅者减少，当前订阅数: ${_eventSubscriberCount[event]!.value}');

      // 如果没有订阅者了，可以考虑清理流（但保留一段时间以防重新订阅）
      if (_eventSubscriberCount[event]!.value <= 0) {
        _eventSubscriberCount[event]!.value = 0;
      }
    }

    // 取消流订阅
    subscription?.cancel();
  }
  
  /// 切换到指定通道
  void switchChannel(int conversationId) {
    // 如果已经在此通道，则不需要操作
    if (_currentConversationId.value == conversationId) {
      LogUtil.debug('已经在通道 $conversationId，无需切换');
      return;
    }

    // 更新当前通道ID
    _currentConversationId.value = conversationId;
    LogUtil.info('已切换到通道: $conversationId');
  }

  /// 切换到全局通道
  ///
  /// 将通道ID设置为null，表示不在任何具体通道中
  /// 通常在角色没有会话ID时使用
  void switchToGlobalChannel() {
    // 将当前通道ID设置为null
    _currentConversationId.value = null;
    LogUtil.info('已切换到全局通道，等待新会话ID分配');
  }
  
  /// 订阅特定通道的事件 - 使用GetX响应式流
  ///
  /// [event] 事件类型
  /// [channelIdOrRoleId] 通道ID或角色ID
  /// 返回订阅流
  Stream<WsMsg> onChannel(WsEvent event, int channelIdOrRoleId) {
    // 检查是否超过最大订阅数
    if (!_messageTracker.recordRoleSubscription(channelIdOrRoleId)) {
      LogUtil.warn('角色ID=$channelIdOrRoleId 的订阅数已达到最大值，使用现有订阅');

      // 如果没有找到现有订阅，强制移除一个订阅以释放空间
      _messageTracker.forceRemoveOldestSubscription(channelIdOrRoleId);
      LogUtil.debug('已强制移除角色ID=${channelIdOrRoleId}的一个旧订阅');
    }

    // 确保通道流存在
    if (!_channelEventStreams.containsKey(channelIdOrRoleId)) {
      _channelEventStreams[channelIdOrRoleId] = <WsMsg>[].obs;
    }

    // 增加订阅者计数
    if (!_channelSubscriberCount.containsKey(channelIdOrRoleId)) {
      _channelSubscriberCount[channelIdOrRoleId] = 0.obs;
    }
    _channelSubscriberCount[channelIdOrRoleId]!.value++;

    LogUtil.debug('已为角色/通道ID=$channelIdOrRoleId创建响应式流订阅，当前订阅数: ${_channelSubscriberCount[channelIdOrRoleId]!.value}');

    // 返回响应式流的stream，展开并过滤指定事件
    return _channelEventStreams[channelIdOrRoleId]!.stream
        .expand((messageList) => messageList)
        .where((message) => message.event == event);
  }
  
  /// 取消特定通道的订阅 - 使用GetX响应式流
  void offChannel(int channelIdOrRoleId, {StreamSubscription? subscription}) {
    // 减少订阅者计数
    if (_channelSubscriberCount.containsKey(channelIdOrRoleId)) {
      _channelSubscriberCount[channelIdOrRoleId]!.value--;
      LogUtil.debug('通道${channelIdOrRoleId}订阅者减少，当前订阅数: ${_channelSubscriberCount[channelIdOrRoleId]!.value}');

      // 如果没有订阅者了，可以考虑清理流（但保留一段时间以防重新订阅）
      if (_channelSubscriberCount[channelIdOrRoleId]!.value <= 0) {
        _channelSubscriberCount[channelIdOrRoleId]!.value = 0;
      }
    }

    // 取消流订阅
    subscription?.cancel();

    // 从MessageTracker中移除订阅记录
    _messageTracker.removeRoleSubscription(channelIdOrRoleId);
    LogUtil.debug('已取消通道ID=$channelIdOrRoleId的订阅');
  }
  
  /// 发送消息到当前通道
  /// 
  /// [event] 事件类型
  /// [data] 消息数据
  /// [channelId] 指定通道ID，如果为null则使用当前通道ID
  /// [messageId] 消息ID，用于追踪
  /// [roleId] 相关角色ID，用于追踪和路由
  bool sendToChannel({
    required WsEvent event,
    required dynamic data,
    int? channelId,
    String? messageId,
    int? roleId,
  }) {
    try {
      // 检查WebSocket是否已连接
      if (!_connectionManager.isConnected) {
        LogUtil.warn('WebSocket未连接，无法发送消息');
        throw WebSocketException('WebSocket未连接，无法发送消息', code: ErrorCodes.WS_MESSAGE_SEND_FAILED);
      }
      
      // 确定使用的通道ID
      final useChannelId = channelId ?? _currentConversationId.value;

      // 构建消息
      final message = WsMsg(
        event: event,
        data: data,
        conversationid: useChannelId,
      );

      // 跟踪消息
      if (messageId != null && roleId != null) {
        _messageTracker.addPendingMessage(messageId, roleId, conversationId: useChannelId);
      }
      
      // 发送消息
      final jsonStr = json.encode(message.toJson());
      _connectionManager.send(jsonStr);
      
      return true;
    } catch (e) {
      LogUtil.error('发送消息失败: $e');
      // 触发WebSocket错误事件
      GlobalEventState.to.triggerWebsocketError({
        'error': e,
        'errorType': 'SEND_FAILED',
        'errorCode': ErrorCodes.WS_MESSAGE_SEND_FAILED,
      });
      return false;
    }
  }
  
  /// 发送消息
  /// 
  /// [message] 要发送的消息
  /// 返回值：bool - 是否发送成功
  bool sendMessage(WsMsg message) {
    try {
      // 检查WebSocket是否已连接
      if (!_connectionManager.isConnected) {
        LogUtil.warn('WebSocket未连接，无法发送消息');
        throw WebSocketException('WebSocket未连接，无法发送消息', code: ErrorCodes.WS_MESSAGE_SEND_FAILED);
      }
      
      final jsonStr = json.encode(message.toJson());
      _connectionManager.send(jsonStr);
      return true;
    } catch (e) {
      LogUtil.error('发送消息失败: $e');
      // 触发WebSocket错误事件
      GlobalEventState.to.triggerWebsocketError({
        'error': e,
        'errorType': 'SEND_FAILED',
        'errorCode': ErrorCodes.WS_MESSAGE_SEND_FAILED,
      });
      return false;
    }
  }
  
  /// 获取当前通道ID
  int? get currentConversationId => _currentConversationId.value;
  
  /// 获取连接状态
  WsConnectionState get connectionState => _connectionManager.connectionState;
  
  /// 获取连接状态流
  Stream<WsConnectionState> get connectionStateStream => _connectionManager.connectionStateStream;
  
  /// 是否已连接
  bool get isConnected => _connectionManager.isConnected;
  
  /// 获取指定角色或通道的缓冲消息
  List<WsMsg> getBufferedMessages(int channelIdOrRoleId) {
    // 不存储缓冲消息，始终返回空列表
    return [];
  }
  
  /// 清除指定角色或通道的缓冲消息
  void clearBufferedMessages(int channelIdOrRoleId) {
    // 不执行任何操作，因为不存储缓冲消息
    LogUtil.debug('不存储缓冲消息，无需清理');
  }
  
  /// 清除所有缓冲消息
  void clearAllBufferedMessages() {
    // 不执行任何操作，因为不存储缓冲消息
    LogUtil.debug('不存储缓冲消息，无需清理');
  }
  
  /// 清除所有响应式流和订阅 - 使用GetX
  void clearAllHandlersAndSubscriptions() {
    try {
      LogUtil.debug('开始清理WsMessageManager的所有响应式流和订阅...');

      // 记录清理前的状态
      final channelIds = _channelEventStreams.keys.toList();
      final eventTypes = _eventStreams.keys.toList();
      int channelStreamsCount = _channelEventStreams.length;
      int eventStreamsCount = _eventStreams.length;

      // 详细记录要清理的通道
      if (channelIds.isNotEmpty) {
        LogUtil.debug('将清理以下通道ID的响应式流: $channelIds');

        // 统计每个通道的消息数量
        for (final channelId in channelIds) {
          final messages = _channelEventStreams[channelId] ?? <WsMsg>[].obs;
          LogUtil.debug('  通道ID=$channelId 的消息数量: ${messages.length}');
        }
      }

      // 清理所有事件流
      _eventStreams.clear();
      _eventSubscriberCount.clear();

      // 清理所有通道流
      _channelEventStreams.clear();
      _channelSubscriberCount.clear();
      
      // 同步清理MessageTracker，确保完全同步
      try {
        final messageTracker = MessageTracker();
        // 这里只清理内部跟踪状态，不清理外部订阅，避免循环调用
        messageTracker.clearAll();
        LogUtil.debug('已同步清理MessageTracker状态');
      } catch (e) {
        LogUtil.error('清理MessageTracker失败: $e');
      }

      LogUtil.info('已清理WsMessageManager: 清理了${channelStreamsCount}个通道响应式流(原通道: $channelIds)和${eventStreamsCount}个事件响应式流(原事件: $eventTypes)');
    } catch (e) {
      LogUtil.error('清理WsMessageManager的所有响应式流和订阅失败: $e');
    }
  }

  /// 销毁
  @override
  void dispose() {
    LogUtil.debug('开始销毁WsMessageManager...');

    // GetX Worker会自动清理，无需手动取消

    // 取消消息订阅
    if (_messageSubscription != null) {
      LogUtil.debug('取消WebSocket消息订阅');
      _messageSubscription?.cancel();
      _messageSubscription = null;
    }

    // 清理所有响应式流
    final eventStreamsCount = _eventStreams.length;
    final channelStreamsCount = _channelEventStreams.length;

    _eventStreams.clear();
    _eventSubscriberCount.clear();
    LogUtil.debug('已清理$eventStreamsCount个事件响应式流');

    _channelEventStreams.clear();
    _channelSubscriberCount.clear();
    LogUtil.debug('已清理$channelStreamsCount个通道响应式流');

    LogUtil.debug('WsMessageManager已销毁');

    // 调用父类dispose
    super.dispose();
  }

  /// 清除指定通道的所有响应式流和订阅 - 使用GetX
  ///
  /// [channelId] 通道ID（通常是角色ID或会话ID）
  /// [isRoleId] 是否是角色ID，如果是则会检查MessageTracker中的订阅状态
  /// 返回是否成功清除
  bool clearChannelHandlers(int channelId, {bool isRoleId = true}) {
    try {
      // 检查该通道是否有响应式流
      if (!_channelEventStreams.containsKey(channelId)) {
        LogUtil.debug('通道ID=$channelId 没有响应式流需要清理');
        return false;
      }

      // 如果是角色ID，检查MessageTracker中的订阅状态
      if (isRoleId) {
        try {
          final messageTracker = MessageTracker();
          final subscriptionCount = messageTracker.getRoleSubscriptionCount(channelId);
          LogUtil.debug('角色ID=$channelId 在MessageTracker中的订阅数: $subscriptionCount');

          // 如果MessageTracker中还有订阅，记录日志，这可能表示不同步
          if (subscriptionCount > 0) {
            LogUtil.warn('警告: 角色ID=$channelId 在MessageTracker中仍有$subscriptionCount个订阅，但正在清理其通道响应式流');
          }
        } catch (e) {
          LogUtil.error('检查角色ID=$channelId 的MessageTracker订阅失败: $e');
        }
      }

      // 获取清理前的消息数量
      final messageCount = _channelEventStreams[channelId]?.length ?? 0;

      // 清理指定通道的响应式流
      _channelEventStreams.remove(channelId);
      _channelSubscriberCount.remove(channelId);

      // 详细记录清理结果
      LogUtil.debug('已清理通道ID=$channelId 的响应式流，包含$messageCount条消息');

      return true;
    } catch (e) {
      LogUtil.error('清理通道ID=$channelId 的响应式流失败: $e');
      return false;
    }
  }
}